import { useState, useCallback, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import Api from '@/services/api';
import { MultisigInfo } from '@/services/types';
import { getSolPrice, calculateTotalVaultValue } from '@/utils/price';

export default function useMultisigModel() {
  const { publicKey } = useWallet();
  const [multisigs, setMultisigs] = useState<MultisigInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const [userBalance, setUserBalance] = useState<number>(0);
  const [solPrice, setSolPrice] = useState<number>(100);
  const [totalVaultValue, setTotalVaultValue] = useState<number>(0);
  const [isInitialized, setIsInitialized] = useState(false); // 标记是否已初始化
  const [hasMultisigAddress, setHasMultisigAddress] = useState<boolean>(true); // 是否有多签地址

  // 缓存时间：5分钟
  const CACHE_DURATION = 5 * 60 * 1000;

  const refreshMultisigs = useCallback(async (forceRefresh = false, pageType?: 'data-required' | 'data-optional' | 'init') => {
    const now = Date.now();

    // 初始化请求：只有第一次调用时才执行
    if (pageType === 'init') {
      if (isInitialized) {
        return; // 已经初始化过，直接返回
      }
      setIsInitialized(true);
    }

    // 如果不是强制刷新且缓存还有效，则不重新请求
    if (!forceRefresh && now - lastFetchTime < CACHE_DURATION && multisigs.length > 0) {
      return;
    }

    // 对于不需要数据的页面（Members、Settings），如果已有数据就不重新加载
    if (pageType === 'data-optional' && multisigs.length > 0 && !forceRefresh) {
      return;
    }

    // Account 页面每次进入都需要最新数据（忽略缓存）
    if (pageType === 'data-required') {
      // Account 页面强制刷新，忽略缓存时间
    }

    try {
      setLoading(true);
      setError(null);

      // 并行获取多签信息、用户余额和 SOL 价格
      const [multisigsResult, solPriceValue] = await Promise.all([
        Api.getMultisigs(),
        getSolPrice(),
      ]);

      setMultisigs(multisigsResult.multisigs);
      setSolPrice(solPriceValue);
      setLastFetchTime(now);
      setHasMultisigAddress(multisigsResult.hasMultisigAddress);

      // 计算总金库价值
      const vaultValue = calculateTotalVaultValue(multisigsResult.multisigs, solPriceValue);
      setTotalVaultValue(vaultValue);

    } catch (err: any) {
      console.error('获取多签信息失败:', err);
      setError(err.message || '获取多签信息失败');
      setMultisigs([]);
      setTotalVaultValue(0);
    } finally {
      setLoading(false);
    }
  }, [lastFetchTime, multisigs.length, isInitialized]);

  // 获取用户钱包余额
  const refreshUserBalance = useCallback(async () => {
    if (!publicKey) {
      setUserBalance(0);
      return;
    }

    try {
      const result = await Api.getUserBalance(publicKey.toBase58());
      setUserBalance(result.balance / 1e9); // 转换为 SOL
    } catch (err: any) {
      console.error('获取用户余额失败:', err);
      setUserBalance(0);
    }
  }, [publicKey]);

  // 当钱包连接状态变化时，刷新用户余额
  useEffect(() => {
    refreshUserBalance();
  }, [refreshUserBalance]);

  // 当前多签账户（通常是第一个，因为后端配置的是固定的）
  const currentMultisig = multisigs.length > 0 ? multisigs[0] : null;

  // 设置多签地址的方法（已禁用，多签地址现在在后端固定配置）
  const setMultisigAddress = useCallback(async (address: string) => {
    console.warn('多签地址已在后端固定配置，无法通过前端修改');
    return {
      success: false,
      message: '多签地址已在后端固定配置，无法通过前端修改。请联系管理员在后端配置多签地址。'
    };
  }, []);

  return {
    multisigs,
    currentMultisig,
    userBalance, // Phantom 钱包的 SOL 余额
    totalVaultValue, // 多签金库总价值（美元）
    solPrice, // SOL 价格
    loading,
    error,
    hasMultisigAddress, // 是否有多签地址
    refreshMultisigs,
    refreshUserBalance,
    setMultisigAddress, // 设置多签地址的方法
    // 为了向后兼容，保留 totalBalance 但指向用户余额
    totalBalance: userBalance,
  };
}
