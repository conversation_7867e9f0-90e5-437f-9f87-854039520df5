import React, { useEffect, useState } from 'react';
import { Table, Button, message, Typography, Space } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { useWallet } from '@solana/wallet-adapter-react';
import { useModel } from '@umijs/max';

const { Text } = Typography;

interface CoinAsset {
  symbol: string;
  address: string;
  balance: number;
  value: number;
  weight: number;
}

const shortAddr = (addr: string) => addr.slice(0, 4) + '...' + addr.slice(-4);

const AccountPage: React.FC = () => {
  const { connected } = useWallet();
  const { multisigs, loading, refreshMultisigs } = useModel('multisig');
  const [assets, setAssets] = useState<CoinAsset[]>([]);

  // Account 页面每次进入都需要最新数据
  useEffect(() => {
    refreshMultisigs(true, 'data-required'); // 强制刷新，忽略缓存
  }, []);

  useEffect(() => {
    const buildAssets = () => {
      if (!connected || multisigs.length === 0) {
        setAssets([]);
        return;
      }

      try {
        const assets: CoinAsset[] = [];

        // 使用后端处理好的资产数据
        multisigs.forEach((multisig: any) => {
          if (multisig.vault.assets) {
            multisig.vault.assets.forEach((asset: any) => {
              assets.push({
                symbol: asset.symbol,
                address: asset.address,
                balance: asset.balance,
                value: asset.value,
                weight: asset.weight,
              });
            });
          }
        });

        setAssets(assets);
      } catch (e) {
        message.error('获取资产失败');
      }
    };
    buildAssets();
  }, [connected, multisigs]);

  const columns = [
    {
      title: 'Coin',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (_: any, record: CoinAsset) => (
        <div>
          <div style={{ fontWeight: 700, fontSize: 16 }}>{record.symbol}</div>
          <Space size={4}>
            <Text type="secondary" style={{ fontSize: 13 }}>{shortAddr(record.address)}</Text>
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => {
                navigator.clipboard.writeText(record.address);
                message.success('已复制');
              }}
            />
          </Space>
        </div>
      )
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (v: number, record: CoinAsset) => (
        <span style={{ fontWeight: 700 }}>
          {v.toFixed(4)} {record.symbol}
        </span>
      )
    },
    {
      title: 'Value (USD)',
      dataIndex: 'value',
      key: 'value',
      render: (v: number) => <span style={{ fontWeight: 700, color: '#52c41a' }}>${v.toFixed(2)}</span>
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      key: 'weight',
      render: (v: number) => (
        <div style={{ minWidth: 80 }}>
          <div style={{ fontWeight: 700 }}>{(v * 100).toFixed(1)}%</div>
          <div style={{ background: '#eee', borderRadius: 4, height: 6, width: 60, marginTop: 2 }}>
            <div style={{
              width: `${Math.min(v * 100, 100)}%`,
              height: '100%',
              background: '#1890ff',
              borderRadius: 4
            }}></div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div>
      <Table
        columns={columns}
        dataSource={assets}
        rowKey={r => r.symbol + r.address}
        loading={loading}
        pagination={false}
        bordered
      />
    </div>
  );
};

export default AccountPage;