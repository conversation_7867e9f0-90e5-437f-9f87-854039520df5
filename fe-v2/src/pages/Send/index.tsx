import React, { useState, useCallback, useEffect } from 'react';
import { Form, Input, Button, Card, Select, message, InputNumber, Space } from 'antd';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { PublicKey, SystemProgram, TransactionMessage, VersionedTransaction, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createTransferInstruction } from '@solana/spl-token';
import * as multisig from '@sqds/multisig';
import { SendOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Recipient } from '@/services/types';
import Api from '@/services/api';

const { Option } = Select;

const SendPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { publicKey, signTransaction } = useWallet();
  const { connection } = useConnection();
  const { currentMultisig, loading: multisigLoading, refreshMultisigs } = useModel('multisig');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [recipients, setRecipients] = useState<Recipient[]>([]);
  const [recipientsLoading, setRecipientsLoading] = useState(false);

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  // 加载收款地址列表
  const loadRecipients = useCallback(async () => {
    try {
      setRecipientsLoading(true);
      const result = await Api.getRecipients();
      setRecipients(result.recipients);
    } catch (error: any) {
      console.error('加载收款地址失败:', error);
      handleError(error.message || '加载收款地址失败');
    } finally {
      setRecipientsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadRecipients();
  }, [loadRecipients]);

  // Send 页面需要最新的余额数据
  useEffect(() => {
    refreshMultisigs(true, 'data-required'); // 强制刷新，获取最新余额
  }, []);

  const validateInputs = (values: any) => {
    if (!publicKey) {
      throw new Error('请先连接钱包');
    }
    if (!currentMultisig) {
      throw new Error('多签账户未加载');
    }

    // 检查用户是否有 Proposer 权限
    const currentUser = currentMultisig.members.find(member => member.key === publicKey.toBase58());
    if (!currentUser) {
      throw new Error('当前钱包不是多签成员');
    }
    if (!currentUser.permissions.canPropose) {
      throw new Error('您没有创建转账交易的权限（需要 Proposer 权限）');
    }

    if (!values.recipient) {
      throw new Error('请选择收款地址');
    }
    if (!values.amount || parseFloat(values.amount) <= 0) {
      throw new Error('请输入有效的转账金额');
    }
    if (!values.selectedToken) {
      throw new Error('请选择币种');
    }

    // 验证地址格式
    try {
      new PublicKey(currentMultisig.address);
    } catch {
      throw new Error('多签地址格式不正确');
    }

    try {
      new PublicKey(values.recipient);
    } catch {
      throw new Error('接收地址格式不正确');
    }

    if (values.selectedToken !== 'SOL') {
      try {
        new PublicKey(values.selectedToken);
      } catch {
        throw new Error('Token合约地址格式不正确');
      }
    }
  };

  const handleTransfer = useCallback(async (values: any) => {
    if (!signTransaction) {
      handleError('钱包不支持签名功能');
      return;
    }

    try {
      setLoading(true);
      validateInputs(values);

      const multisigPda = new PublicKey(currentMultisig!.address);
      const recipientPubkey = new PublicKey(values.recipient);

      // 使用当前多签信息
      const multisigInfo = currentMultisig!;

      // 检查当前用户是否是多签成员
      const isMember = multisigInfo.members.some(member => member.key === publicKey!.toBase58());
      if (!isMember) {
        throw new Error('当前钱包不是多签成员');
      }

      const vaultPda = new PublicKey(multisigInfo.vault.address);
      let transferInstruction;
      const isTokenTransfer = values.selectedToken !== 'SOL';
      let tokenAccount = null;
      let tokenName = 'SOL';

      if (isTokenTransfer) {
        // Token 转账
        const tokenMintPubkey = new PublicKey(values.selectedToken);
        tokenAccount = multisigInfo.vault.tokenAccounts.find(
          t => t.mint === values.selectedToken
        );

        if (!tokenAccount) {
          throw new Error('未找到对应的Token账户');
        }

        tokenName = tokenAccount.name;
        const transferAmount = Math.floor(parseFloat(values.amount) * Math.pow(10, tokenAccount.decimals));

        if (tokenAccount.balance < transferAmount) {
          throw new Error(`${tokenAccount.name}余额不足。当前余额: ${tokenAccount.uiAmountString} ${tokenAccount.name}`);
        }

        const vaultTokenAccount = await getAssociatedTokenAddress(
          tokenMintPubkey,
          vaultPda,
          true
        );
        const recipientTokenAccount = await getAssociatedTokenAddress(
          tokenMintPubkey,
          recipientPubkey
        );

        transferInstruction = createTransferInstruction(
          vaultTokenAccount,
          recipientTokenAccount,
          vaultPda,
          transferAmount,
          [],
          TOKEN_PROGRAM_ID
        );
      } else {
        // SOL 转账
        const transferAmount = Math.floor(parseFloat(values.amount) * LAMPORTS_PER_SOL);

        if (multisigInfo.vault.balance < transferAmount) {
          throw new Error(`SOL余额不足。当前余额: ${multisigInfo.vault.balanceSOL} SOL`);
        }

        transferInstruction = SystemProgram.transfer({
          fromPubkey: vaultPda,
          toPubkey: recipientPubkey,
          lamports: transferAmount,
        });
      }

      const { blockhash } = await Api.getBlockhash();
      const transferMessage = new TransactionMessage({
        payerKey: vaultPda,
        recentBlockhash: blockhash,
        instructions: [transferInstruction],
      });

      const nextTransactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;

      // 创建交易并投票
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: `Transfer ${values.amount} ${tokenName} to ${values.recipient.substring(0, 8)}...`,
        programId,
      });

      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey!,
        isDraft: false,
        programId,
      });

      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        member: publicKey!,
        programId,
      });

      const combinedMessage = new TransactionMessage({
        payerKey: publicKey!,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      });

      const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message());
      const signedTx = await signTransaction(combinedTx);

      // 发送交易
      const result = isTokenTransfer
        ? await Api.createTokenTransfer({
            multisigAddress: currentMultisig!.address,
            recipientAddress: values.recipient,
            tokenMint: values.selectedToken,
            amount: values.amount,
            decimals: tokenAccount!.decimals,
            creatorPublicKey: publicKey!.toBase58(),
            signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
          })
        : await Api.createSolTransfer({
            multisigAddress: currentMultisig!.address,
            recipientAddress: values.recipient,
            amount: values.amount,
            creatorPublicKey: publicKey!.toBase58(),
            signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
          });

      messageApi.success('转账交易创建成功！等待其他成员投票...');
      handleSuccess(result.signature);

    } catch (error: any) {
      console.error('转账失败:', error);
      handleError(error.message || '转账失败');
    } finally {
      setLoading(false);
    }
  }, [publicKey, signTransaction, currentMultisig, connection]);

  return (
    <>
      {contextHolder}
      <div>
        <Card loading={multisigLoading}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleTransfer}
            initialValues={{
              selectedToken: 'SOL',
            }}
          >
            {/* From 字段 - 显示金库地址 */}
            <Form.Item label="From">
              <Input
                value={currentMultisig?.vault.address || ''}
                disabled
                placeholder="金库地址"
                style={{ backgroundColor: '#f5f5f5' }}
              />
            </Form.Item>

            {/* To 字段 - 下拉选择收款地址 */}
            <Form.Item
              name="recipient"
              label="To"
              rules={[{ required: true, message: '请选择收款地址' }]}
            >
              <Select
                placeholder="选择收款地址"
                loading={recipientsLoading}
                optionFilterProp="children"
              >
                {recipients.map((recipient) => (
                  <Option key={recipient.address} value={recipient.address}>
                    <div>
                      <div style={{ fontSize: '12px', color: '#666' }}>{recipient.address}</div>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {/* Amount 字段 - 金额输入 + 币种选择 */}
            <Form.Item label="Amount">
              <Space.Compact style={{ width: '100%' }}>
                <Form.Item
                  name="amount"
                  noStyle
                  rules={[{ required: true, message: '请输入转账金额' }]}
                >
                  <InputNumber placeholder="0" style={{ width: '60%' }} />
                </Form.Item>
                <Form.Item
                  name="selectedToken"
                  noStyle
                  rules={[{ required: true, message: '请选择币种' }]}
                >
                  <Select placeholder="选择币种" style={{ width: '40%' }}>
                    {/* SOL 选项 */}
                    <Option value="SOL">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>SOL</span>
                        <span style={{ fontSize: '12px', color: '#666' }}>
                          余额: {currentMultisig?.vault.balanceSOL.toFixed(4) || '0.0000'} SOL
                        </span>
                      </div>
                    </Option>
                    {/* Token 选项 */}
                    {currentMultisig?.vault.tokenAccounts.map((token) => (
                      <Option key={token.mint} value={token.mint}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span>{token.name}</span>
                          <span style={{ fontSize: '12px', color: '#666' }}>
                            余额: {token.uiAmountString} {token.name}
                          </span>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Space.Compact>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                disabled={!publicKey || !currentMultisig}
                icon={<SendOutlined />}
                block
              >
                创建转账交易
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </>
  );
};

export default SendPage;