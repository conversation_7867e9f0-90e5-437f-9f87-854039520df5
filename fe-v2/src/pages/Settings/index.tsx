import React, { useState } from 'react'
import { Card, Descriptions, Space, Form, Input, Button, message, Alert } from 'antd'
import { useModel } from '@umijs/max'
import AddressCopy from '@/components/AddressCopy'

const Settings: React.FC = () => {
  const {
    currentMultisig: multisig,
    loading,
    hasMultisigAddress,
    setMultisigAddress
  } = useModel('multisig')

  const [form] = Form.useForm()
  const [settingAddress, setSettingAddress] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()

  const handleSetMultisigAddress = async (values: { address: string }) => {
    try {
      setSettingAddress(true)
      const result = await setMultisigAddress(values.address.trim())

      if (result.success) {
        messageApi.success(result.message)
        form.resetFields()
      } else {
        messageApi.error(result.message)
      }
    } catch (error: any) {
      messageApi.error(error.message || '设置多签地址失败')
    } finally {
      setSettingAddress(false)
    }
  }



  return (
    <>
      {contextHolder}
      <div>
        {/* 多签地址设置 */}
        {!hasMultisigAddress && (
          <Card title="设置多签地址" style={{ marginBottom: 16 }}>
            <Alert
              message="请设置多签地址"
              description="您需要先设置一个有效的多签地址才能使用系统功能。"
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSetMultisigAddress}
            >
              <Form.Item
                label="Multisig Account"
                name="address"
                rules={[
                  { required: true, message: '请输入多签地址' },
                  { min: 32, message: '地址长度不正确' }
                ]}
              >
                <Input
                  placeholder="请输入多签地址，例如：HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr"
                  size="large"
                />
              </Form.Item>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={settingAddress}
                  size="large"
                >
                  设置多签地址
                </Button>
              </Form.Item>
            </Form>
          </Card>
        )}

        {/* 多签信息展示 */}
        {hasMultisigAddress && multisig && (
          <Card title="多签信息" loading={loading}>
            <Descriptions bordered>
              <Descriptions.Item label="Members" span={3}>
                <div>{multisig?.members.length}</div>
              </Descriptions.Item>
              <Descriptions.Item label="Threshold" span={3}>
                <Space>{multisig?.threshold}/{multisig?.members?.length ? multisig?.members?.length - 1 : 0}</Space>
              </Descriptions.Item>
              <Descriptions.Item label="Squad Vault" span={3}>
                <AddressCopy address={multisig?.vault.address || ''} label="Squad Vault" showFullAddress={true} />
              </Descriptions.Item>
              <Descriptions.Item label="Multisig Account" span={3}>
                <AddressCopy address={multisig?.address || ''} label="Multisig Account" showFullAddress={true} />
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}

        {/* 更换多签地址 */}
        {hasMultisigAddress && (
          <Card title="更换多签地址" style={{ marginTop: 16 }}>
            <Alert
              message="更换多签地址"
              description="如果需要更换多签地址，请在下方输入新的地址。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            <Form
              layout="vertical"
              onFinish={handleSetMultisigAddress}
            >
              <Form.Item
                label="新的 Multisig Account"
                name="address"
                rules={[
                  { required: true, message: '请输入多签地址' },
                  { min: 32, message: '地址长度不正确' }
                ]}
              >
                <Input
                  placeholder="请输入新的多签地址"
                  size="large"
                />
              </Form.Item>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={settingAddress}
                  size="large"
                >
                  更换多签地址
                </Button>
              </Form.Item>
            </Form>
          </Card>
        )}
      </div>
    </>
  )
}

export default Settings
