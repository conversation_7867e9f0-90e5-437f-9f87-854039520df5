const Router = require('koa-router');
const multisig = require('@sqds/multisig');
const { PublicKey } = require('@solana/web3.js');
const axios = require('axios');
const { TOKEN_NAME_MAP, RECIPIENTS } = require('./constants');
const {
  MULTISIG_PROGRAM_ID,
  connection,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout,
  getCurrentMultisigAddress,
  hasConfiguredMultisigAddress
} = require('./utils');

// 获取 Token 名称
const getTokenName = (mint) => {
  return TOKEN_NAME_MAP[mint] || `Token(${mint.slice(0, 4)}...${mint.slice(-4)})`;
};

const router = new Router();

// ==================== 权限和价格工具函数 ====================

// Squads 权限位掩码常量
const PERMISSION_MASKS = {
  PROPOSER: 0x01,  // 0b001 - 可以创建提案
  VOTER: 0x02,     // 0b010 - 可以投票
  EXECUTOR: 0x04,  // 0b100 - 可以执行交易
};

/**
 * 解析权限掩码，返回具体权限信息
 */
function parsePermissions(permissions) {
  let mask = 0;

  // 如果 permissions 是对象且有 mask 属性
  if (permissions && typeof permissions === 'object' && 'mask' in permissions) {
    mask = permissions.mask;
  } else if (typeof permissions === 'number') {
    mask = permissions;
  } else {
    // 默认情况：假设有所有权限（向后兼容）
    return {
      canPropose: true,
      canVote: true,
      canExecute: true,
      labels: ['Proposer', 'Voter', 'Executor']
    };
  }

  const canPropose = (mask & PERMISSION_MASKS.PROPOSER) !== 0;
  const canVote = (mask & PERMISSION_MASKS.VOTER) !== 0;
  const canExecute = (mask & PERMISSION_MASKS.EXECUTOR) !== 0;

  const labels = [];
  if (canPropose) labels.push('Proposer');
  if (canVote) labels.push('Voter');
  if (canExecute) labels.push('Executor');

  return {
    canPropose,
    canVote,
    canExecute,
    labels: labels.length > 0 ? labels : ['无权限']
  };
}

/**
 * 获取 SOL 价格（美元）
 */
async function getSolPrice() {
  try {
    const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd', {
      timeout: 5000
    });
    return response.data.solana?.usd || 100; // 默认价格 100 USD
  } catch (error) {
    console.warn('获取 SOL 价格失败，使用默认价格:', error.message);
    return 100; // 默认价格
  }
}

/**
 * 获取 Token 价格（美元）
 */
function getTokenPrice(symbol) {
  const tokenPrices = {
    'USDC': 1,
    'USDT': 1,
    'SOL': 100, // 会被实时价格覆盖
    'mSOL': 95,
    'BONK': 0.00001,
    'ETH': 2500,
    'BTC': 45000,
  };
  return tokenPrices[symbol] || 0;
}

/**
 * 计算资产权重占比
 */
function calculateAssetWeights(assets) {
  const totalValue = assets.reduce((sum, asset) => sum + asset.value, 0);

  return assets.map(asset => ({
    ...asset,
    weight: totalValue > 0 ? asset.value / totalValue : 0
  }));
}

// ==================== 系统接口 ====================

// 健康检查
router.get('/health', async (ctx) => {
  const { SOLANA_NETWORK, NODE_ENV } = require('./constants');
  ctx.body = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    network: SOLANA_NETWORK,
    environment: NODE_ENV
  };
});

// 获取区块哈希
router.get('/api/blockhash', async (ctx) => {
  try {
    const { blockhash } = await connection.getLatestBlockhash();
    ctx.body = { blockhash };
  } catch (error) {
    handleError(ctx, error, '获取区块哈希失败');
  }
});

// ==================== 账户信息接口 ====================

// 获取多签账户信息
router.get('/api/multisigs', async (ctx) => {
  try {
    const multisigs = [];

    // 获取当前设置的多签地址
    const currentAddress = getCurrentMultisigAddress();

    // 如果没有设置多签地址，返回空数组
    if (!currentAddress) {
      ctx.body = {
        multisigs: [],
        solPrice: await getSolPrice(),
        timestamp: new Date().toISOString(),
        hasMultisigAddress: false
      };
      return;
    }

    // 获取实时 SOL 价格
    const solPrice = await getSolPrice();

    try {
      const multisigPubkey = new PublicKey(currentAddress);
      const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

      // 获取金库信息
      const [vaultPda] = multisig.getVaultPda({
        multisigPda: multisigPubkey,
        index: 0,
        programId: MULTISIG_PROGRAM_ID
      });

      const vaultBalance = await connection.getBalance(vaultPda);

      // 获取金库的 Token 账户信息
      const tokenAccounts = [];
      try {
        const tokenAccountsResponse = await connection.getParsedTokenAccountsByOwner(vaultPda, {
          programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
        });

        for (const tokenAccountInfo of tokenAccountsResponse.value) {
          const parsedInfo = tokenAccountInfo.account.data.parsed.info;
          const tokenAmount = parsedInfo.tokenAmount;

          // 只包含有余额的 Token 账户
          if (tokenAmount.uiAmount > 0) {
            const tokenName = getTokenName(parsedInfo.mint);
            const tokenPrice = tokenName === 'SOL' ? solPrice : getTokenPrice(tokenName);

            tokenAccounts.push({
              address: tokenAccountInfo.pubkey.toBase58(),
              mint: parsedInfo.mint,
              name: tokenName,
              balance: parseInt(tokenAmount.amount),
              decimals: tokenAmount.decimals,
              uiAmount: tokenAmount.uiAmount,
              uiAmountString: tokenAmount.uiAmountString,
              price: tokenPrice,
              value: tokenAmount.uiAmount * tokenPrice
            });
          }
        }
      } catch (tokenError) {
        console.error(`获取金库 ${vaultPda.toBase58()} Token 账户失败:`, tokenError.message);
      }

      // 构建资产列表（包含 SOL 和 Token）
      const assets = [];

      // 添加 SOL 资产
      if (vaultBalance > 0) {
        assets.push({
          symbol: 'SOL',
          address: vaultPda.toBase58(),
          balance: vaultBalance / 1e9,
          price: solPrice,
          value: (vaultBalance / 1e9) * solPrice,
          weight: 0 // 将在后面计算
        });
      }

      // 添加 Token 资产
      tokenAccounts.forEach(token => {
        assets.push({
          symbol: token.name,
          address: token.address,
          balance: token.uiAmount,
          price: token.price,
          value: token.value,
          weight: 0 // 将在后面计算
        });
      });

      // 计算权重占比
      const assetsWithWeights = calculateAssetWeights(assets);

      multisigs.push({
        address: currentAddress,
        members: multisigData.members.map(member => {
          const permissions = parsePermissions(member.permissions);
          return {
            key: member.key.toBase58(),
            permissions: {
              ...permissions,
              raw: member.permissions // 保留原始权限数据
            }
          };
        }),
        threshold: multisigData.threshold,
        transactionIndex: Number(multisigData.transactionIndex.toString()),
        createKey: multisigData.createKey.toBase58(),
        allowExternalExecute: multisigData.allowExternalExecute,
        rentCollector: multisigData.rentCollector?.toBase58(),
        vault: {
          address: vaultPda.toBase58(),
          balance: vaultBalance,
          balanceSOL: vaultBalance / 1e9,
          tokenAccounts,
          assets: assetsWithWeights, // 新增：处理好的资产列表
          totalValue: assetsWithWeights.reduce((sum, asset) => sum + asset.value, 0) // 新增：总价值
        }
      });
    } catch (error) {
      console.error(`获取多签账户 ${currentAddress} 信息失败:`, error.message);
      // 如果获取失败，返回空数组
      ctx.body = {
        multisigs: [],
        solPrice,
        timestamp: new Date().toISOString(),
        hasMultisigAddress: true,
        error: `多签地址 ${currentAddress} 无效或无法访问`
      };
      return;
    }

    ctx.body = {
      multisigs,
      solPrice, // 返回 SOL 价格供前端使用
      timestamp: new Date().toISOString(),
      hasMultisigAddress: true
    };
  } catch (error) {
    handleError(ctx, error, '获取多签账户信息失败');
  }
});

// 获取用户账户余额
router.post('/api/account', async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['publicKey']);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { publicKey } = ctx.request.body;
    const userPublicKey = new PublicKey(publicKey);

    // 获取用户 SOL 余额
    const balance = await connection.getBalance(userPublicKey);

    ctx.body = { balance };
  } catch (error) {
    handleError(ctx, error, '获取账户余额失败');
  }
});

// 获取收款地址列表
router.get('/api/recipients', async (ctx) => {
  try {
    ctx.body = { recipients: RECIPIENTS };
  } catch (error) {
    handleError(ctx, error, '获取收款地址列表失败');
  }
});

// ==================== 转账接口 ====================

// 创建转账交易（合并SOL和Token转账）
router.post('/api/transfer', async (ctx) => {
  try {
    const { type, multisigAddress, recipientAddress, amount, creatorPublicKey, signedTransaction } = ctx.request.body;

    let requiredParams = ['type', 'multisigAddress', 'recipientAddress', 'amount', 'creatorPublicKey', 'signedTransaction'];

    // Token转账需要额外参数
    if (type === 'token') {
      requiredParams.push('tokenMint', 'decimals');
    }

    const error = validateParams(ctx.request.body, requiredParams);
    if (error) return handleError(ctx, { status: 400 }, error);

    const signature = await sendTransaction(signedTransaction);

    if (type === 'sol') {
      const confirmation = await confirmTransactionWithTimeout(signature);

      if (confirmation.confirmed) {
        ctx.body = {
          signature,
          result: confirmation.result,
          success: true,
          message: `SOL转账成功: ${amount} SOL to ${recipientAddress.substring(0, 8)}...`
        };
      } else {
        ctx.body = {
          signature,
          success: true,
          confirmed: false,
          message: `SOL转账已发送: ${amount} SOL to ${recipientAddress.substring(0, 8)}...`,
          note: '交易确认超时，请稍后检查交易状态',
          explorerUrl: `https://explorer.solana.com/tx/${signature}?cluster=mainnet-beta`
        };
      }
    } else {
      const result = await connection.confirmTransaction(signature);
      ctx.body = {
        signature,
        result,
        success: true,
        message: `Token转账成功: ${amount} Token to ${recipientAddress.substring(0, 8)}...`
      };
    }
  } catch (error) {
    let errorMessage = `创建${ctx.request.body.type === 'sol' ? 'SOL' : 'Token'}转账失败`;
    if (error.message?.includes('insufficient funds')) {
      errorMessage = '账户余额不足';
    } else if (error.message?.includes('Transaction simulation failed')) {
      errorMessage = '交易模拟失败，请检查参数';
    } else if (error.message?.includes('Blockhash not found')) {
      errorMessage = '区块哈希过期，请重新发起交易';
    }
    handleError(ctx, error, errorMessage);
  }
});

// ==================== 交易管理接口 ====================

// 获取交易列表
router.post('/api/transactions', async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['multisigAddress']);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress } = ctx.request.body;
    const multisigPubkey = new PublicKey(multisigAddress);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    const transactions = [];
    const currentTransactionIndex = Number(multisigData.transactionIndex.toString());
    const startIndex = Math.max(1, currentTransactionIndex - MAX_PROPOSALS_TO_CHECK);

    for (let i = startIndex; i <= currentTransactionIndex; i++) {
      try {
        const [proposalPda] = multisig.getProposalPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        const proposalAccount = await connection.getAccountInfo(proposalPda);
        if (!proposalAccount) continue;

        const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];

        // 获取交易备注
        const [transactionPda] = multisig.getTransactionPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        const transactionAccount = await connection.getAccountInfo(transactionPda);
        let memo = '';
        if (transactionAccount) {
          try {
            const transactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
            memo = transactionData.memo || '';
          } catch (err) {
            // 忽略解析错误
          }
        }

        const approvals = proposalData.approved.length;
        const threshold = multisigData.threshold;

        let status = 'Active';
        if (proposalData.status.__kind === 'Executed') status = 'Executed';
        else if (proposalData.status.__kind === 'Rejected') status = 'Rejected';
        else if (proposalData.status.__kind === 'Cancelled') status = 'Cancelled';
        else if (approvals >= threshold) status = 'Approved';

        const votes = [
          ...proposalData.approved.map(member => ({ member: member.toBase58(), vote: 'Approve' })),
          ...proposalData.rejected.map(member => ({ member: member.toBase58(), vote: 'Reject' }))
        ];

        // 基于测试结果，这个多签账户无法获取详细的转账信息
        // 我们只能提供基本的交易信息
        let transferAmount = null;
        let transferToken = null;
        let toAddress = null;
        let transactionType = 'unknown';

        // 尝试从memo解析（如果有的话）
        if (memo) {
          const amountMatch = memo.match(/Transfer\s+([\d.]+)\s+(\w+)/);
          if (amountMatch) {
            transferAmount = parseFloat(amountMatch[1]);
            transferToken = amountMatch[2];
            transactionType = 'transfer';
          }

          const addressMatch = memo.match(/to\s+([A-Za-z0-9]{8,})/);
          if (addressMatch) {
            toAddress = addressMatch[1];
          }
        }

        // 获取创建时间和创建者
        let createdAt = null;
        let realCreator = 'Unknown';

        // 推断创建者：优先使用 creator，其次使用第一个批准者
        if (proposalData.creator) {
          realCreator = proposalData.creator.toBase58();
        } else if (proposalData.approved && proposalData.approved.length > 0) {
          realCreator = proposalData.approved[0].toBase58();
        }

        // 🎯 关键改进：从 ProposalStatus.timestamp 获取真实创建时间
        try {
          if (proposalData.status && proposalData.status.timestamp) {
            // 时间戳是 Unix 时间戳（秒）
            const timestamp = Number(proposalData.status.timestamp.toString());
            createdAt = new Date(timestamp * 1000).toISOString();
          }
        } catch (err) {
          // 如果解析时间戳失败，继续使用估算时间
        }

        // 如果没有获取到真实时间，使用估算时间
        if (!createdAt) {
          const baseTime = new Date('2024-01-01T00:00:00Z').getTime();
          const estimatedTime = new Date(baseTime + (i - 1) * 60 * 60 * 1000);
          createdAt = estimatedTime.toISOString();
        }

        transactions.push({
          transactionIndex: i,
          status,
          approvals,
          threshold,
          creator: realCreator,
          memo,
          votes,
          canExecute: status === 'Approved',
          // 新增字段
          transactionType,
          transferAmount,
          transferToken,
          toAddress,
          createdAt
        });
      } catch (err) {
        // 静默跳过解析失败的交易
      }
    }

    transactions.sort((a, b) => b.transactionIndex - a.transactionIndex);
    ctx.body = { transactions };
  } catch (error) {
    handleError(ctx, error, '获取交易列表失败');
  }
});

// 构建执行指令（必须在动态路由之前）
router.post('/api/transactions/build-execute', async (ctx) => {
  try {
    const requiredParams = ['multisigAddress', 'transactionIndex', 'executorPublicKey'];
    const error = validateParams(ctx.request.body, requiredParams);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress, transactionIndex, executorPublicKey } = ctx.request.body;
    const multisigPda = new PublicKey(multisigAddress);
    const executorPubkey = new PublicKey(executorPublicKey);

    const instructionResult = await multisig.instructions.vaultTransactionExecute({
      connection,
      multisigPda,
      transactionIndex: BigInt(transactionIndex),
      member: executorPubkey,
      programId: MULTISIG_PROGRAM_ID,
    });

    if (!instructionResult) throw new Error('指令构建返回空结果');

    let instruction;
    if (instructionResult.keys && instructionResult.programId && instructionResult.data !== undefined) {
      instruction = instructionResult;
    } else if (instructionResult.instruction) {
      instruction = instructionResult.instruction;
    } else if (Array.isArray(instructionResult) && instructionResult.length > 0) {
      instruction = instructionResult[0];
    } else {
      throw new Error('无法解析指令结果格式');
    }

    if (!instruction.keys || !instruction.programId || instruction.data === undefined) {
      throw new Error('指令格式不完整');
    }

    ctx.body = {
      instruction: {
        keys: instruction.keys.map(key => ({
          pubkey: key.pubkey.toBase58(),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: instruction.programId.toBase58(),
        data: Array.from(instruction.data)
      },
      lookupTableAccounts: []
    };
  } catch (error) {
    handleError(ctx, error, '构建执行指令失败');
  }
});

// 交易操作（投票和执行）
router.post('/api/transactions/:action', async (ctx) => {
  try {
    const { action } = ctx.params;

    if (!['vote', 'execute'].includes(action)) {
      return handleError(ctx, { status: 400 }, '无效的操作类型');
    }

    let requiredParams = ['multisigAddress', 'transactionIndex', 'userPublicKey', 'signedTransaction'];

    if (action === 'vote') {
      requiredParams.push('vote');
    }

    const error = validateParams(ctx.request.body, requiredParams);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress, transactionIndex, signedTransaction } = ctx.request.body;

    if (action === 'execute') {
      // 执行前验证
      const multisigPubkey = new PublicKey(multisigAddress);

      const [proposalPda] = multisig.getProposalPda({
        multisigPda: multisigPubkey,
        transactionIndex: BigInt(transactionIndex),
        programId: MULTISIG_PROGRAM_ID
      });

      const proposalAccount = await connection.getAccountInfo(proposalPda);
      if (!proposalAccount) {
        return handleError(ctx, { status: 400 }, `交易 #${transactionIndex} 不存在`);
      }

      const [transactionPda] = multisig.getTransactionPda({
        multisigPda: multisigPubkey,
        index: BigInt(transactionIndex),
        programId: MULTISIG_PROGRAM_ID
      });

      const transactionAccount = await connection.getAccountInfo(transactionPda);
      if (!transactionAccount) {
        return handleError(ctx, { status: 400 }, `交易 #${transactionIndex} 没有对应的交易内容，无法执行`);
      }

      const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
      const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

      const approvals = proposalData.approved.length;
      const threshold = multisigData.threshold;

      if (approvals < threshold) {
        return handleError(ctx, { status: 400 }, `交易投票不足，需要 ${threshold} 票，当前只有 ${approvals} 票`);
      }

      if (proposalData.status.__kind === 'Executed') {
        return handleError(ctx, { status: 400 }, `交易 #${transactionIndex} 已经执行过了`);
      }

      if (proposalData.status.__kind === 'Cancelled') {
        return handleError(ctx, { status: 400 }, `交易 #${transactionIndex} 已被取消`);
      }

      const executorPubkey = new PublicKey(ctx.request.body.userPublicKey);
      const isMember = multisigData.members.some(member => member.key.equals(executorPubkey));
      if (!isMember) {
        return handleError(ctx, { status: 400 }, '执行者不是多签成员');
      }
    }

    const signature = await sendTransaction(signedTransaction);
    const result = await connection.confirmTransaction(signature);

    ctx.body = {
      signature,
      result,
      success: true,
      message: action === 'vote' ? '投票提交成功' : '交易执行成功'
    };
  } catch (error) {
    const actionName = ctx.params.action === 'vote' ? '投票' : '执行交易';
    handleError(ctx, error, `${actionName}失败`);
  }
});

module.exports = router;