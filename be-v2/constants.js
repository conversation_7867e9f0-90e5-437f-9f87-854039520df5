const { PublicKey } = require('@solana/web3.js');

// ==================== 网络配置 ====================
const SOLANA_NETWORK = 'mainnet-beta';
const SOLANA_RPC_URL = 'https://api.mainnet-beta.solana.com';
const SOLANA_COMMITMENT = 'confirmed';

// ==================== 程序配置 ====================
const MULTISIG_PROGRAM_ID = new PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf');

// ==================== 多签地址配置 ====================
// 请在这里设置你的多签地址
const FIXED_MULTISIG_ADDRESS = 'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr'; // 例如：'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr'

// ==================== 交易配置 ====================
const TX_TIMEOUT = 30000; // 30秒
const TX_MAX_RETRIES = 3;
const MAX_PROPOSALS_TO_CHECK = 50;

// ==================== 服务器配置 ====================
const PORT = 3001;
const NODE_ENV = 'development';

// ==================== Token 配置 ====================
const TOKEN_NAME_MAP = {
  'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
  'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
  'So11111111111111111111111111111111111111112': 'SOL',
  'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So': 'mSOL',
  'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': 'BONK',
  '7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs': 'ETH',
  '9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E': 'BTC'
};

// ==================== 收款地址配置 ====================
const RECIPIENTS = [
  {
    name: '钱包组内钱包',
    address: '2hvBjn1R3nSXHJop7dz2xTTWSLkQiHSjPmpyjdQY7p7Y',
    description: '用于功能测试'
  }
];

module.exports = {
  // 网络配置
  SOLANA_NETWORK,
  SOLANA_RPC_URL,
  SOLANA_COMMITMENT,

  // 程序配置
  MULTISIG_PROGRAM_ID,

  // 多签地址配置
  FIXED_MULTISIG_ADDRESS,

  // 交易配置
  TX_TIMEOUT,
  TX_MAX_RETRIES,
  MAX_PROPOSALS_TO_CHECK,

  // 服务器配置
  PORT,
  NODE_ENV,

  // Token 配置
  TOKEN_NAME_MAP,

  // 收款地址配置
  RECIPIENTS
};
