const { Connection } = require('@solana/web3.js');
const {
  MULTISIG_PROGRAM_ID,
  SOLANA_RPC_URL,
  SOLANA_COMMITMENT,
  TX_TIMEOUT,
  TX_MAX_RETRIES,
  MAX_PROPOSALS_TO_CHECK,
  FIXED_MULTISIG_ADDRESS
} = require('./constants');

// 创建 Solana 连接
const connection = new Connection(SOLANA_RPC_URL, SOLANA_COMMITMENT);

// 多签地址管理函数
const getCurrentMultisigAddress = () => {
  return FIXED_MULTISIG_ADDRESS;
};

// 检查是否有配置的多签地址
const hasConfiguredMultisigAddress = () => {
  return FIXED_MULTISIG_ADDRESS !== null && FIXED_MULTISIG_ADDRESS !== '';
};

// 工具函数
const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key]);
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null;
};

const handleError = (ctx, error, message) => {
  const status = error.status || 500;
  ctx.status = status;
  ctx.body = { error: message || error.message };
};

const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: SOLANA_COMMITMENT,
      maxRetries: TX_MAX_RETRIES,
      ...options
    }
  );
  return signature;
};

const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, SOLANA_COMMITMENT),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ]);
    return { confirmed: true, result };
  } catch (error) {
    return { confirmed: false, error: error.message };
  }
};

module.exports = {
  MULTISIG_PROGRAM_ID,
  connection,
  TX_TIMEOUT,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout,
  getCurrentMultisigAddress,
  hasConfiguredMultisigAddress
};