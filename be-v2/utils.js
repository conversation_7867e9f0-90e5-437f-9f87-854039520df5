require('dotenv').config();
const { Connection, PublicKey } = require('@solana/web3.js');

// 从环境变量获取常量
const MULTISIG_PROGRAM_ID = new PublicKey(process.env.MULTISIG_PROGRAM_ID);
const connection = new Connection(process.env.SOLANA_RPC_URL, process.env.SOLANA_COMMITMENT);
const TX_TIMEOUT = parseInt(process.env.TX_TIMEOUT);
const MAX_PROPOSALS_TO_CHECK = parseInt(process.env.MAX_PROPOSALS_TO_CHECK);

// 固定的多签地址配置（在这里配置你的多签地址）
const FIXED_MULTISIG_ADDRESS = null; // 请在这里设置你的多签地址，例如：'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr'

// 多签地址管理函数
const getCurrentMultisigAddress = () => {
  return FIXED_MULTISIG_ADDRESS;
};

// 检查是否有配置的多签地址
const hasConfiguredMultisigAddress = () => {
  return FIXED_MULTISIG_ADDRESS !== null && FIXED_MULTISIG_ADDRESS !== '';
};

// 保留这个函数以保持向后兼容，但实际上不会修改固定配置
const setCurrentMultisigAddress = (address) => {
  console.warn('警告：多签地址已在后端固定配置，无法通过接口修改。请在 utils.js 中的 FIXED_MULTISIG_ADDRESS 变量中配置多签地址。');
  console.log('尝试设置的地址:', address);
  console.log('当前配置的地址:', FIXED_MULTISIG_ADDRESS);
};

// 工具函数
const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key]);
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null;
};

const handleError = (ctx, error, message) => {
  const status = error.status || 500;
  ctx.status = status;
  ctx.body = { error: message || error.message };
};

const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: process.env.SOLANA_COMMITMENT,
      maxRetries: parseInt(process.env.TX_MAX_RETRIES),
      ...options
    }
  );
  return signature;
};

const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, process.env.SOLANA_COMMITMENT),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ]);
    return { confirmed: true, result };
  } catch (error) {
    return { confirmed: false, error: error.message };
  }
};

module.exports = {
  MULTISIG_PROGRAM_ID,
  connection,
  TX_TIMEOUT,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout,
  getCurrentMultisigAddress,
  hasConfiguredMultisigAddress,
  setCurrentMultisigAddress
};